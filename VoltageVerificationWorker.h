#ifndef VOLTAGEVERIFICATIONWORKER_H
#define VOLTAGEVERIFICATIONWORKER_H

#include <QObject>
#include <QThread>
#include <QMessageBox>
#include <QDebug>
#include <QTimer>
#include "WorkerConfig.h"

class VoltageVerificationWorker : public QObject
{
    Q_OBJECT

public:
    explicit VoltageVerificationWorker(QObject *parent = nullptr);
    ~VoltageVerificationWorker();

    void setDeviceConfig(const AdjDeviceConfig &config);
    void requestAbort();

    // 设置命令发送函数指针
    typedef QPair<bool, QString> (*CommandHandlerFunc)(const QByteArray &, const QString &);
    void setCommandHandlers(CommandHandlerFunc adjHandler);

public slots:
    void startVoltageVerification();
    void onUserPromptResult(bool confirmed);
    void recalibrateChannel(int channel, int level);

signals:
    void logMessage(const QString &message);
    void voltageVerificationFinished(bool success);
    void voltageVerificationProgress(int currentStep, int totalSteps);
    void updateVoltageData(int channel, int level, double voltage, double deviation, bool passed);
    void voltageLevelCompleted(int channel, int level, double finalVoltage, double finalDeviation, bool levelPassed);
    void requestUserPrompt(const QString &message);
    void clearVoltageTable();

private slots:

private:
    struct VoltageLevel
    {
        double targetVoltage; // 目标电压值 (mV)
        QString description;  // 描述文本
    };

    AdjDeviceConfig m_deviceConfig;
    bool m_abortRequested;
    QTimer *m_dataCollectionTimer;

    // 命令处理函数指针
    CommandHandlerFunc m_adjCommandHandler;

    // 验证状态
    int m_currentChannel;              // 当前验证通道 (0-7 for CH1-CH8)
    int m_currentLevel;                // 当前验证档位 (0-4 for -10,10,30,50,75mV)
    int m_currentAttempt;              // 当前尝试次数
    int m_currentRound;                // 当前读取轮次
    QVector<double> m_voltageReadings; // 电压读取值
    QVector<double> m_deviations;      // 偏差值

    static const QVector<VoltageLevel> VOLTAGE_LEVELS;
    static const int MAX_ATTEMPTS = 3;
    static const int READINGS_PER_LEVEL = 4;  // 每个档位读取4次
    static const int WAIT_TIME_MS = 2000;     // 等待2秒
    static const int READ_INTERVAL_MS = 1000; // 1秒间隔
    static const double VOLTAGE_THRESHOLD;    // 允差阈值

    // 私有方法
    void showUserPrompt(int channel, double targetVoltage);
    void startDataCollection();
    void collectVoltageData();
    void moveToNextLevel();
    void moveToNextChannel();
    void finishVerification(bool success);
    int calculateProgress() const;
    double calculateAllowedDeviation(double measuredVoltage);
    bool isChannelLevelPassed(const QVector<double> &voltageReadings);

    // 通信相关
    QPair<bool, QString> sendCommand(const QByteArray &command, const QString &commandId);
    QByteArray createModbusCommand(const QString &hexString);
    QByteArray createModbusReadFrame(quint8 deviceAddr, quint16 startAddr, int channel, int count);
    double readVoltageValue(int channel);
    uint16_t calculateCRC16(const QByteArray &data);
    quint16 getDeviceReadAddress();
};

#endif // VOLTAGEVERIFICATIONWORKER_H
