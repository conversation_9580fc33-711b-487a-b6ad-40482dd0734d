# 1618A-N/L TC型号电压验证功能实现总结

## 功能概述

为1618A-N/L设备新增了TC型号的电压验证功能，实现了半自动的电压校准流程，支持-10、10、30、50、75mV五种参考电压的验证。

## 主要实现内容

### 1. 新增文件

#### VoltageVerificationWorker.h
- 电压验证工作类的头文件
- 定义了电压验证的核心逻辑和数据结构
- 包含5个电压档位：-10mV、10mV、30mV、50mV、75mV
- 支持8通道验证

#### VoltageVerificationWorker.cpp (已存在，功能完善)
- 实现了半自动电压验证流程
- 支持用户交互式操作（754设备连接提示）
- 每个档位读取4次，1秒间隔
- 自动计算允差：0.0001*测量值+0.01mV

### 2. 修改的文件

#### VerificationDialog.cpp
- 新增 `updateVerificationButtonStates()` 函数
- 根据读取的板卡类型（TC-xx）自动切换显示模式
- TC类型显示电压验证表格，非TC类型显示电阻验证表格
- 新增电压验证按钮的槽函数实现

#### VerificationDialog.h
- 添加电压验证相关的槽函数声明
- 完善了TC型号检测和组件显示控制的函数声明

#### mainwindow.cpp
- 新增 `initializeVoltageVerificationTable()` 函数
- 新增电压验证数据更新处理函数
- 修改 `startVerificationInThread()` 支持TC类型检测
- 添加电压验证线程和工作对象的创建和管理
- 完善信号连接和事件处理

#### mainwindow.h
- 添加VoltageVerificationWorker相关的头文件包含
- 新增电压验证相关的成员变量和函数声明

### 3. UI界面改进

#### VerificationDialog.ui (已存在)
- groupBox_7已包含完整的电压验证界面
- 包含手动校准、停止校准、复校、保存等按钮
- 包含readDataTable_Adj_2表格控件用于显示验证数据

## 核心功能特性

### 1. 自动类型检测
- 读取板卡精度时自动检测是否为TC类型
- TC类型自动显示电压验证界面
- 非TC类型显示传统电阻验证界面

### 2. 半自动验证流程
```
STEP1：点击"手动校准"按钮
STEP2：弹窗提示连接754到指定通道，设置输出电压
STEP3：用户确认后等待2秒，连续读取4次（1秒间隔）
STEP4：实时更新测量值、偏差、允差到表格
STEP5：重复直至所有通道所有档位完成
```

### 3. 数据处理
- **测量值**：显示实际读取的电压值（mV）
- **偏差**：测量值与参考值的差值（mV）
- **允差**：0.0001*测量值+0.01mV
- **结果**：基于4次读取平均值判断合格/不合格

### 4. 设备地址配置
- **1618A-N TC读取地址**：0x033D (03 3D)
- **1618A-L TC读取地址**：0x015D (01 5D)
- 自动根据设备型号选择正确的通信地址

### 5. 表格显示
- 8通道 × 5档位 = 40行数据
- 列结构：通道 | 参考电压(mV) | 测量电压(mV) | 偏差(mV) | 允差(mV) | 结果
- 实时更新验证进度和结果
- 合格/不合格用绿色/红色字体区分

## 技术实现要点

### 1. 线程安全
- 使用独立的工作线程处理电压验证
- 通过Qt信号槽机制实现线程间通信
- 避免UI阻塞，保证用户体验

### 2. 用户交互
- 模态对话框提示用户操作
- 支持用户取消操作
- 实时进度显示

### 3. 错误处理
- 通信失败重试机制
- 用户取消操作的优雅处理
- 设备地址自动识别和容错

### 4. 代码复用
- 复用现有的Modbus通信框架
- 复用设备配置和数据处理逻辑
- 保持与现有校准流程的一致性

## 使用流程

1. 选择1618A-N或1618A-L设备进入验证界面
2. 读取板卡编号和板卡精度
3. 系统自动检测到TC类型，显示电压验证界面
4. 点击"手动校准"开始验证
5. 按提示连接754设备到各通道
6. 系统自动完成8通道×5档位的验证
7. 查看验证结果并保存

## 兼容性说明

- 完全兼容现有的电阻验证功能
- 不影响其他设备型号的验证流程
- 保持原有UI布局和操作习惯
- 支持透传模式设备的特殊要求

## 后续扩展

- 可根据需要添加更多电压档位
- 可扩展支持其他TC型号设备
- 可增加验证结果的详细报告功能
- 可添加验证数据的导出功能
