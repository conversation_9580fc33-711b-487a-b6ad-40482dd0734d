#include "VoltageVerificationWorker.h"
#include <QApplication>
#include <QThread>
#include <cmath>

// 静态常量定义
const QVector<VoltageVerificationWorker::VoltageLevel> VoltageVerificationWorker::VOLTAGE_LEVELS = {
    {-10.0, "-10mV"},
    {10.0, "10mV"},
    {30.0, "30mV"},
    {50.0, "50mV"},
    {75.0, "75mV"}
};

const double VoltageVerificationWorker::VOLTAGE_THRESHOLD = 0.01; // 10μV = 0.01mV

VoltageVerificationWorker::VoltageVerificationWorker(QObject *parent)
    : QObject(parent),
      m_abortRequested(false), 
      m_dataCollectionTimer(new QTimer(this)), 
      m_currentChannel(0),
      m_currentLevel(0), 
      m_currentAttempt(0), 
      m_currentRound(0), 
      m_adjCommandHandler(nullptr)
{
    m_dataCollectionTimer->setSingleShot(true);
    connect(m_dataCollectionTimer, &QTimer::timeout, this, &VoltageVerificationWorker::collectVoltageData);
}

VoltageVerificationWorker::~VoltageVerificationWorker()
{
}

void VoltageVerificationWorker::setDeviceConfig(const AdjDeviceConfig &config)
{
    m_deviceConfig = config;
}

void VoltageVerificationWorker::setCommandHandlers(CommandHandlerFunc adjHandler)
{
    m_adjCommandHandler = adjHandler;
}

void VoltageVerificationWorker::requestAbort()
{
    m_abortRequested = true;
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }

    emit logMessage("电压验证被用户中止");
    emit voltageVerificationFinished(false);
}

void VoltageVerificationWorker::startVoltageVerification()
{
    emit logMessage("开始1618A-N/L TC型号电压验证...");
    emit logMessage(QString("设备型号: %1").arg(m_deviceConfig.name));

    // 清空表格数据
    emit clearVoltageTable();

    m_abortRequested = false;
    m_currentChannel = 0;  // 从CH1开始
    m_currentLevel = 0;    // 从-10mV开始
    m_currentAttempt = 1;

    // 初始进度条：0%开始
    emit voltageVerificationProgress(0, 100);

    // 开始第一个通道第一个电压档位的验证
    showUserPrompt(m_currentChannel, VOLTAGE_LEVELS[m_currentLevel].targetVoltage);
}

void VoltageVerificationWorker::showUserPrompt(int channel, double targetVoltage)
{
    if (m_abortRequested)
        return;

    QString message = QString("请将754连接至通道%1，设置输出%2mV后点击确定").arg(channel + 1).arg(targetVoltage);
    emit logMessage(QString("等待用户设置754输出%1mV到通道%2...").arg(targetVoltage).arg(channel + 1));

    // 发送信号到主线程显示对话框
    emit requestUserPrompt(message);
}

void VoltageVerificationWorker::onUserPromptResult(bool confirmed)
{
    if (m_abortRequested)
        return;

    if (confirmed)
    {
        double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
        emit logMessage(QString("用户确认设置完成，开始验证通道%1的%2档位...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));

        // 等待2秒后开始数据采集
        QTimer::singleShot(WAIT_TIME_MS, this, &VoltageVerificationWorker::startDataCollection);
    }
    else
    {
        emit logMessage("用户取消电压验证");
        finishVerification(false);
    }
}

void VoltageVerificationWorker::startDataCollection()
{
    if (m_abortRequested)
        return;

    // 重置读取状态
    m_currentRound = 0;
    m_voltageReadings.clear();
    m_deviations.clear();

    emit logMessage(QString("开始采集通道%1的%2档位数据...").arg(m_currentChannel + 1).arg(VOLTAGE_LEVELS[m_currentLevel].description));

    // 开始第一次读取
    collectVoltageData();
}

void VoltageVerificationWorker::collectVoltageData()
{
    if (m_abortRequested)
        return;

    if (m_currentRound >= READINGS_PER_LEVEL)
    {
        // 读取完成，分析结果
        double averageVoltage = 0.0;
        double averageDeviation = 0.0;
        
        for (int i = 0; i < m_voltageReadings.size(); ++i)
        {
            averageVoltage += m_voltageReadings[i];
            averageDeviation += m_deviations[i];
        }
        
        averageVoltage /= m_voltageReadings.size();
        averageDeviation /= m_deviations.size();
        
        // 计算允差
        double allowedDeviation = calculateAllowedDeviation(averageVoltage);
        bool levelPassed = qAbs(averageDeviation) <= allowedDeviation;
        
        emit logMessage(QString("通道%1的%2档位验证完成: 平均电压=%.6fmV, 平均偏差=%.6fmV, 允差=%.6fmV, 结果=%3")
                       .arg(m_currentChannel + 1)
                       .arg(VOLTAGE_LEVELS[m_currentLevel].description)
                       .arg(averageVoltage, 0, 'f', 6)
                       .arg(averageDeviation, 0, 'f', 6)
                       .arg(allowedDeviation, 0, 'f', 6)
                       .arg(levelPassed ? "合格" : "不合格"));

        // 发送档位完成信号
        emit voltageLevelCompleted(m_currentChannel, m_currentLevel, averageVoltage, averageDeviation, levelPassed);
        
        // 移动到下一档位或下一通道
        moveToNextLevel();
        return;
    }

    // 读取电压值
    double voltage = readVoltageValue(m_currentChannel);
    double targetVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
    double deviation = voltage - targetVoltage;
    double allowedDeviation = calculateAllowedDeviation(voltage);
    bool passed = qAbs(deviation) <= allowedDeviation;

    m_voltageReadings.append(voltage);
    m_deviations.append(deviation);

    emit logMessage(QString("通道%1第%2次读取: %.6fmV (偏差: %3%.6fmV)")
                   .arg(m_currentChannel + 1)
                   .arg(m_currentRound + 1)
                   .arg(voltage, 0, 'f', 6)
                   .arg(deviation >= 0 ? "+" : "")
                   .arg(deviation, 0, 'f', 6));

    // 更新界面数据
    emit updateVoltageData(m_currentChannel, m_currentLevel, voltage, deviation, passed);

    m_currentRound++;

    // 如果还需要继续读取，设置定时器
    if (m_currentRound < READINGS_PER_LEVEL)
    {
        m_dataCollectionTimer->start(READ_INTERVAL_MS);
    }
    else
    {
        // 立即处理最后一次读取的结果
        collectVoltageData();
    }
}

void VoltageVerificationWorker::moveToNextLevel()
{
    if (m_abortRequested)
        return;

    m_currentLevel++;
    
    if (m_currentLevel >= VOLTAGE_LEVELS.size())
    {
        // 当前通道所有档位完成，移动到下一通道
        moveToNextChannel();
    }
    else
    {
        // 继续当前通道的下一档位
        m_currentAttempt = 1;
        emit voltageVerificationProgress(calculateProgress(), 100);
        showUserPrompt(m_currentChannel, VOLTAGE_LEVELS[m_currentLevel].targetVoltage);
    }
}

void VoltageVerificationWorker::moveToNextChannel()
{
    if (m_abortRequested)
        return;

    m_currentChannel++;
    m_currentLevel = 0;  // 重置到第一个档位
    
    if (m_currentChannel >= m_deviceConfig.num_channels)
    {
        // 所有通道完成
        emit logMessage("所有通道的电压验证已完成");
        finishVerification(true);
    }
    else
    {
        // 继续下一通道
        m_currentAttempt = 1;
        emit voltageVerificationProgress(calculateProgress(), 100);
        showUserPrompt(m_currentChannel, VOLTAGE_LEVELS[m_currentLevel].targetVoltage);
    }
}

void VoltageVerificationWorker::recalibrateChannel(int channel, int level)
{
    emit logMessage(QString("开始复校通道%1的%2档位...").arg(channel + 1).arg(VOLTAGE_LEVELS[level].description));
    
    m_currentChannel = channel;
    m_currentLevel = level;
    m_currentAttempt = 1;
    
    showUserPrompt(channel, VOLTAGE_LEVELS[level].targetVoltage);
}

double VoltageVerificationWorker::readVoltageValue(int channel)
{
    if (!m_adjCommandHandler)
    {
        emit logMessage("错误：命令处理器未设置");
        return 0.0;
    }

    // 获取设备读取地址
    quint16 readAddr = getDeviceReadAddress();
    
    // 创建Modbus读取命令：功能码03，起始地址，通道偏移，读取数量
    QString hexCommand = QString("01 03 %1 %2 00 01")
                        .arg(QString::number((readAddr >> 8) & 0xFF, 16).toUpper().rightJustified(2, '0'))
                        .arg(QString::number((readAddr + channel) & 0xFF, 16).toUpper().rightJustified(2, '0'));
    
    QByteArray command = QByteArray::fromHex(hexCommand.replace(" ", "").toLatin1());
    
    // 计算并添加CRC
    // 这里需要实现CRC计算，暂时简化
    
    QPair<bool, QString> result = m_adjCommandHandler(command, "Adj_ReadVoltage");
    
    if (result.first)
    {
        // 解析返回的电压值
        bool ok;
        double voltage = result.second.toDouble(&ok);
        if (ok)
        {
            return voltage;
        }
        else
        {
            emit logMessage(QString("解析电压值失败: %1").arg(result.second));
            return 0.0;
        }
    }
    else
    {
        emit logMessage(QString("读取通道 %1 电压失败: %2").arg(channel + 1).arg(result.second));
        return 0.0;
    }
}

double VoltageVerificationWorker::calculateAllowedDeviation(double measuredVoltage)
{
    // 允差计算公式：0.0001*测量值+0.01mV
    return 0.0001 * qAbs(measuredVoltage) + 0.01;
}

bool VoltageVerificationWorker::isChannelLevelPassed(const QVector<double> &deviations)
{
    if (deviations.size() < 3)
        return false;

    // 检查最后三次测量的偏差是否都在允差内
    for (int i = deviations.size() - 3; i < deviations.size(); ++i)
    {
        double allowedDev = VOLTAGE_THRESHOLD;
        if (qAbs(deviations[i]) > allowedDev)
            return false;
    }

    return true;
}

int VoltageVerificationWorker::calculateProgress() const
{
    int totalSteps = m_deviceConfig.num_channels * VOLTAGE_LEVELS.size();
    int completedSteps = m_currentChannel * VOLTAGE_LEVELS.size() + m_currentLevel;
    return (completedSteps * 100) / totalSteps;
}

quint16 VoltageVerificationWorker::getDeviceReadAddress()
{
    // 根据设备型号返回读取首地址
    if (m_deviceConfig.name.startsWith("1618A-N"))
    {
        return 0x033D; // 1618A-N的TC读取首地址：03 3D
    }
    else if (m_deviceConfig.name.startsWith("1618A-L"))
    {
        return 0x015D; // 1618A-L的TC读取首地址：01 5D
    }
    else
    {
        emit logMessage("警告：未知的设备型号，使用默认地址");
        return 0x033D; // 默认使用1618A-N的地址
    }
}

void VoltageVerificationWorker::finishVerification(bool success)
{
    // 更新进度条为100%
    emit voltageVerificationProgress(100, 100);
    
    if (success)
    {
        emit logMessage("1618A-N/L TC型号电压验证成功完成！");
    }
    else
    {
        emit logMessage("1618A-N/L TC型号电压验证失败或被中止！");
    }
    
    emit voltageVerificationFinished(success);
} 