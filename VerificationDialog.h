﻿#ifndef VERIFICATIONDIALOG_H
#define VERIFICATIONDIALOG_H
#include "ui_VerificationDialog.h"
#include <QDialog>
#include <QTimer>

namespace Ui
{
    class VerificationDialog;
}

class VerificationDialog : public QDialog
{
    Q_OBJECT

public:
    explicit VerificationDialog(QWidget *parent = nullptr);
    ~VerificationDialog();

    void initVerificationDialog(const QString &deviceModel);      // 传入设备型号
    void updateVerificationButtonStates(const QString &cardType); // TC型号检测和组件显示控制
    Ui::VerificationDialog *getUi() { return ui; }

public slots:
    void on_signal_readCommand_AdjDialog_Result(const QString &serialNumber, const QString &featureCode);
    // void on_signal_writeCommand_AdjDialog_Result(const QString &result, const QString &featureCode);

private slots:
    void closeEvent(QCloseEvent *event);
    void on_readBtn_AdjDialog_clicked();
    void on_readBtn_Board_AdjDialog_clicked();
    void on_readBtn_Board_AdjDialog_2_clicked();
    void on_startCal_Adj_clicked();

private:
    Ui::VerificationDialog *ui;
    void loadSettings();
    QTimer *resizeTimer;

protected:
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;

signals:
    void signal_readCommand_AdjDialog(const QString &deviceModel, const QString &featureCode);
    // void signal_writeCommand_AdjDialog(const QString &deviceModel, const QString &featureCode, const QString &paramVal);
    void signal_startCal_AdjDialog(const QString &deviceModel, const QString &featureCode);
};

#endif // VERIFICATIONDIALOG_H
